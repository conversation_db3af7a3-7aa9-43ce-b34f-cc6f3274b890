#!/bin/bash

# Financial Tracking Application Setup Script
# This script automates the setup process for the application

echo "🚀 Financial Tracking Application Setup"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running on macOS, Linux, or Windows (Git Bash)
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
    else
        OS="unknown"
    fi
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check PHP
    if command -v php &> /dev/null; then
        PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
        print_status "PHP $PHP_VERSION found"
    else
        print_error "PHP not found. Please install PHP 8.0 or higher."
        exit 1
    fi
    
    # Check Composer
    if command -v composer &> /dev/null; then
        print_status "Composer found"
    else
        print_error "Composer not found. Please install Composer."
        exit 1
    fi
    
    # Check MySQL
    if command -v mysql &> /dev/null; then
        print_status "MySQL found"
    else
        print_warning "MySQL not found. Please ensure MySQL is installed and running."
    fi
}

# Install dependencies
install_dependencies() {
    print_info "Installing PHP dependencies..."
    composer install
    if [ $? -eq 0 ]; then
        print_status "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
}

# Setup environment file
setup_environment() {
    print_info "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_status "Environment file created from template"
        
        # Get current directory for session path
        CURRENT_DIR=$(pwd)
        SESSION_PATH="$CURRENT_DIR/writable/session"
        
        # Update session path in .env file
        if [[ "$OS" == "macos" ]] || [[ "$OS" == "linux" ]]; then
            sed -i.bak "s|/absolute/path/to/your/project/writable/session|$SESSION_PATH|g" .env
            rm .env.bak
        elif [[ "$OS" == "windows" ]]; then
            sed -i "s|/absolute/path/to/your/project/writable/session|$SESSION_PATH|g" .env
        fi
        
        print_status "Session path updated to: $SESSION_PATH"
        print_warning "Please update database credentials in .env file"
    else
        print_warning ".env file already exists. Skipping..."
    fi
}

# Setup directories and permissions
setup_directories() {
    print_info "Setting up directories and permissions..."
    
    # Create writable directories if they don't exist
    mkdir -p writable/logs
    mkdir -p writable/cache
    mkdir -p writable/session
    mkdir -p writable/uploads
    
    # Set permissions
    if [[ "$OS" == "macos" ]] || [[ "$OS" == "linux" ]]; then
        chmod -R 755 writable/
        chmod -R 755 public/
        print_status "Permissions set successfully"
    else
        print_warning "Please ensure writable/ directory has write permissions"
    fi
}

# Database setup
setup_database() {
    print_info "Database setup..."
    print_warning "Please ensure MySQL is running and create the database manually:"
    echo ""
    echo "For Local Development:"
    echo "  mysql -u root -p"
    echo "  CREATE DATABASE org_accounting;"
    echo "  EXIT;"
    echo "  mysql -u root -p org_accounting < database_export.sql"
    echo ""
    echo "For Hostinger Production:"
    echo "  1. Create database through Hostinger control panel"
    echo "  2. Import database_export.sql through phpMyAdmin"
    echo "  3. Update .env file with Hostinger database credentials"
    echo ""
}

# Main setup function
main() {
    detect_os
    print_info "Detected OS: $OS"
    
    check_prerequisites
    install_dependencies
    setup_environment
    setup_directories
    setup_database
    
    echo ""
    print_status "Setup completed successfully!"
    echo ""
    print_info "Next steps:"
    echo "1. Update database credentials in .env file"
    echo "2. Create and import database (see instructions above)"
    echo "3. Start the application: php spark serve --port=8082"
    echo "4. Open browser: http://localhost:8082"
    echo "5. Login with: Username: boss, Password: admin123"
    echo ""
    print_warning "Remember to change default login credentials after first login!"
}

# Run main function
main
