<?php

namespace App\Controllers;

use App\Models\MemberModel;
use App\Models\CommitmentModel;
use App\Models\PaymentModel;
use App\Models\CollectionSummaryModel;
use CodeIgniter\Controller;

class Reports extends BaseController
{
    protected $memberModel;
    protected $commitmentModel;
    protected $paymentModel;
    protected $summaryModel;

    public function __construct()
    {
        $this->memberModel = new MemberModel();
        $this->commitmentModel = new CommitmentModel();
        $this->paymentModel = new PaymentModel();
        $this->summaryModel = new CollectionSummaryModel();
    }

    /**
     * Display the dashboard with summary reports
     *
     * @return mixed
     */
    public function index()
    {
        $db = \Config\Database::connect();

        // Get total members
        $totalMembers = $this->memberModel->countAllResults();
        $activeMembers = $this->memberModel->where('status', 'active')->countAllResults();

        // Get total commitments using the new calculation method
        $totalCommitments = 0;
        $members = $this->memberModel->findAll();
        foreach ($members as $member) {
            $totalCommitments += $this->commitmentModel->calculateTotalCommitment($member['member_id']);
        }

        // Get total payments
        $totalPayments = $this->paymentModel->selectSum('amount')->first()['amount'] ?? 0;

        // Get total outstanding balance
        $totalOutstanding = $this->summaryModel->selectSum('balance')->first()['balance'] ?? 0;

        // Get recent payments
        $recentPayments = $this->paymentModel->getPaymentsWithMemberDetails();
        $recentPayments = array_slice($recentPayments, 0, 5);

        // Get members with outstanding balances
        $membersWithOutstandingBalances = $this->summaryModel->getMembersWithOutstandingBalances();

        // Add phone and WhatsApp numbers to the members with outstanding balances
        $db = \Config\Database::connect();
        foreach ($membersWithOutstandingBalances as &$member) {
            $memberData = $db->table('members')
                ->select('phone, whatsapp_number')
                ->where('member_id', $member['member_id'])
                ->get()
                ->getRowArray();

            $member['phone'] = $memberData['phone'] ?? '';
            $member['whatsapp_number'] = $memberData['whatsapp_number'] ?? '';
        }

        $membersWithOutstandingBalances = array_slice($membersWithOutstandingBalances, 0, 5);

        // Get active members without active commitments
        $membersWithoutActiveCommitments = $this->getMembersWithoutActiveCommitments();
        $membersWithoutActiveCommitments = array_slice($membersWithoutActiveCommitments, 0, 5);

        // Calculate member payment compliance data
        $summaries = $this->summaryModel->getAllSummariesWithMemberDetails(false);

        // Initialize counters for each category
        $fullyPaid = 0;      // 100% paid
        $mostlyPaid = 0;     // 75-99% paid
        $partiallyPaid = 0;  // 25-74% paid
        $minimallyPaid = 0;  // 1-24% paid
        $noPaid = 0;         // 0% paid

        foreach ($summaries as $summary) {
            $totalCommitted = (float)$summary['total_committed'];
            $totalPaid = (float)$summary['total_paid'];

            if ($totalCommitted <= 0) {
                // Skip members with no commitments
                continue;
            }

            $paymentRatio = $totalPaid / $totalCommitted * 100;

            if ($paymentRatio >= 100) {
                $fullyPaid++;
            } elseif ($paymentRatio >= 75) {
                $mostlyPaid++;
            } elseif ($paymentRatio >= 25) {
                $partiallyPaid++;
            } elseif ($paymentRatio > 0) {
                $minimallyPaid++;
            } else {
                $noPaid++;
            }
        }

        // No sample data - show actual empty state

        $memberComplianceData = [
            'labels' => ['Fully Paid (100%)', 'Mostly Paid (75-99%)', 'Partially Paid (25-74%)', 'Minimally Paid (1-24%)', 'No Payments (0%)'],
            'data' => [$fullyPaid, $mostlyPaid, $partiallyPaid, $minimallyPaid, $noPaid],
            'backgroundColor' => ['#28a745', '#5cb85c', '#ffc107', '#fd7e14', '#dc3545']
        ];

        // Get payment method distribution
        $paymentMethods = $db->table('payments')
            ->select('payment_method, COUNT(*) as count')
            ->groupBy('payment_method')
            ->get()
            ->getResultArray();

        $methodLabels = [];
        $methodData = [];
        $methodColors = [];

        $colorMap = [
            'cash' => '#28a745',
            'check' => '#6f42c1',
            'other' => '#6c757d',
            'bank transfer' => '#007bff',
            'credit card' => '#fd7e14',
            'mobile money' => '#17a2b8'
        ];

        foreach ($paymentMethods as $method) {
            $methodName = ucfirst($method['payment_method'] ?? 'Other');
            $methodLabels[] = $methodName;
            $methodData[] = (int)$method['count'];
            $methodColors[] = $colorMap[strtolower($method['payment_method'] ?? 'other')] ?? '#6c757d';
        }

        // No sample data - show actual empty state

        $paymentMethodData = [
            'labels' => $methodLabels,
            'data' => $methodData,
            'backgroundColor' => $methodColors
        ];

        // Calculate collection efficiency metrics using period-based logic
        $collectionEfficiencyData = $this->calculateCollectionEfficiencyMetrics();

        // Get statistics data with date range filter
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-12 months', strtotime($endDate)));

        // Generate collection summary chart data using the same logic as collection summary report
        $collectionChartData = $this->generateCollectionSummaryChartData($startDate, $endDate);

        $months = $collectionChartData['months'];
        $committedAmounts = $collectionChartData['committedAmounts'];
        $paidAmounts = $collectionChartData['paidAmounts'];
        $memberCounts = $collectionChartData['memberCounts'];
        $fulfillmentRates = []; // Calculate fulfillment rates from the data

        // Calculate fulfillment rates
        for ($i = 0; $i < count($committedAmounts); $i++) {
            $committed = $committedAmounts[$i];
            $paid = $paidAmounts[$i];
            $rate = ($committed > 0) ? ($paid / $committed * 100) : 0;
            $fulfillmentRates[] = round($rate, 1);
        }

        // Generate date-based payment data for Payment Trend Analysis
        $paymentTrendData = $this->generatePaymentTrendData($startDate, $endDate);
        $datePaidAmounts = $paymentTrendData['paidAmounts'];

        // Calculate moving average for payment trend (3-month) using date-based data
        $movingAverages = [];
        for ($i = 0; $i < count($datePaidAmounts); $i++) {
            if ($i < 2) {
                // For the first two months, use available data
                $sum = 0;
                $count = 0;
                for ($j = 0; $j <= $i; $j++) {
                    $sum += $datePaidAmounts[$j];
                    $count++;
                }
                $movingAverages[] = $count > 0 ? $sum / $count : 0;
            } else {
                // For subsequent months, use 3-month moving average
                $movingAverages[] = ($datePaidAmounts[$i] + $datePaidAmounts[$i-1] + $datePaidAmounts[$i-2]) / 3;
            }
        }

        // Add fulfillment rates (percentage of commitments paid)
        $fulfillmentRates = [];
        for ($i = 0; $i < count($committedAmounts); $i++) {
            $committedAmount = $committedAmounts[$i];
            $paidAmount = $paidAmounts[$i];
            $fulfillmentRate = ($committedAmount > 0) ? ($paidAmount / $committedAmount * 100) : 0;
            $fulfillmentRates[] = round($fulfillmentRate, 1); // Round to 1 decimal place
        }

        $data = [
            'title' => 'Dashboard',
            'totalMembers' => $totalMembers,
            'activeMembers' => $activeMembers,
            'totalCommitments' => $totalCommitments,
            'totalPayments' => $totalPayments,
            'totalOutstanding' => $totalOutstanding,
            'recentPayments' => $recentPayments,
            'membersWithOutstandingBalances' => $membersWithOutstandingBalances,
            'membersWithoutActiveCommitments' => $membersWithoutActiveCommitments,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'months' => json_encode($months),
            'committedAmounts' => json_encode($committedAmounts),
            'paidAmounts' => json_encode($paidAmounts),
            'memberCounts' => json_encode($memberCounts),
            'datePaidAmounts' => json_encode($datePaidAmounts),
            'fulfillmentRates' => json_encode($fulfillmentRates),
            'movingAverages' => json_encode($movingAverages),
            'memberComplianceData' => json_encode($memberComplianceData),
            'paymentMethodData' => json_encode($paymentMethodData),
            'collectionEfficiencyData' => $collectionEfficiencyData
        ];

        return view('reports/dashboard', $data);
    }

    /**
     * Generate collection summary chart data based on commitment months and payment periods
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    private function generateCollectionSummaryChartData($startDate, $endDate)
    {
        // Convert to DateTime objects for easier manipulation
        $startDateTime = new \DateTime($startDate);
        $endDateTime = new \DateTime($endDate);

        // Ensure start date is the first day of the month
        $startDateTime->modify('first day of this month');
        $startDate = $startDateTime->format('Y-m-d');

        // Ensure end date is the last day of the month
        $endDateTime->modify('last day of this month');
        $endDate = $endDateTime->format('Y-m-d');

        // Calculate the number of months in the range
        $interval = $startDateTime->diff($endDateTime);
        $monthCount = ($interval->y * 12) + $interval->m + 1; // +1 to include both start and end months

        // Initialize arrays to store data for each month
        $months = [];
        $committedAmounts = [];
        $paidAmounts = [];

        // Loop through each month in the range
        $currentDate = clone $startDateTime;
        for ($i = 0; $i < $monthCount; $i++) {
            // Format month for display (e.g., "Jan 2023")
            $monthLabel = $currentDate->format('M Y');
            $months[] = $monthLabel;

            // Get Y-m format for this month
            $currentMonth = $currentDate->format('Y-m');

            // Calculate committed amount for this specific month using the same logic as collection summary
            $committedAmount = $this->calculateTotalCommittedForMonth($currentMonth);
            $committedAmounts[] = $committedAmount;

            // Calculate paid amount for this specific month using payment periods
            $paidAmount = $this->calculateTotalPaidForMonth($currentMonth);
            $paidAmounts[] = $paidAmount;

            // Move to next month
            $currentDate->modify('+1 month');
        }

        // Calculate member counts for each month
        $memberCounts = [];
        $currentDate = clone $startDateTime;
        for ($i = 0; $i < $monthCount; $i++) {
            $currentMonth = $currentDate->format('Y-m');
            try {
                $memberCounts[] = $this->calculateMemberCountForMonth($currentMonth);
            } catch (\Exception $e) {
                log_message('error', 'Error calculating member count for chart data: ' . $e->getMessage());
                $memberCounts[] = 0; // Default to 0 if calculation fails
            }
            $currentDate->modify('+1 month');
        }

        return [
            'months' => $months,
            'committedAmounts' => $committedAmounts,
            'paidAmounts' => $paidAmounts,
            'memberCounts' => $memberCounts
        ];
    }

    /**
     * Generate payment trend data based on actual payment dates (not payment periods)
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    private function generatePaymentTrendData($startDate, $endDate)
    {
        // Convert to DateTime objects for easier manipulation
        $startDateTime = new \DateTime($startDate);
        $endDateTime = new \DateTime($endDate);

        // Ensure start date is the first day of the month
        $startDateTime->modify('first day of this month');
        $startDate = $startDateTime->format('Y-m-d');

        // Ensure end date is the last day of the month
        $endDateTime->modify('last day of this month');
        $endDate = $endDateTime->format('Y-m-d');

        // Calculate the number of months in the range
        $interval = $startDateTime->diff($endDateTime);
        $monthCount = ($interval->y * 12) + $interval->m + 1; // +1 to include both start and end months

        // Initialize arrays to store data for each month
        $paidAmounts = [];

        $db = \Config\Database::connect();

        // Loop through each month in the range
        $currentDate = clone $startDateTime;
        for ($i = 0; $i < $monthCount; $i++) {
            // Get first and last day of current month
            $firstDay = $currentDate->format('Y-m-01');
            $lastDay = $currentDate->format('Y-m-t');

            // Calculate total paid amount for this month based on payment_date
            $paidQuery = $db->table('payments')
                ->select('SUM(amount) as total')
                ->where('payment_date >=', $firstDay)
                ->where('payment_date <=', $lastDay)
                ->get();

            $paidResult = $paidQuery->getRowArray();
            $paidAmounts[] = (float)($paidResult['total'] ?? 0);

            // Move to next month
            $currentDate->modify('+1 month');
        }

        return [
            'paidAmounts' => $paidAmounts
        ];
    }

    /**
     * Calculate total committed amount for a specific month
     * Uses the same logic as collection summary - based on commitment months, not creation dates
     *
     * @param string $month Y-m format
     * @return float
     */
    private function calculateTotalCommittedForMonth($month)
    {
        $db = \Config\Database::connect();

        $commitments = $db->table('commitments')->get()->getResultArray();
        $totalCommitted = 0;

        foreach ($commitments as $commitment) {
            $amount = $commitment['amount'];
            $frequency = $commitment['frequency'];
            $startDate = $commitment['start_date'];
            $endDate = $commitment['end_date'];

            if ($frequency === 'one-time') {
                // For one-time commitments, check if start_date falls within the month
                $commitmentMonth = date('Y-m', strtotime($startDate));
                if ($commitmentMonth === $month) {
                    $totalCommitted += $amount;
                }
            } else if ($frequency === 'monthly') {
                // For monthly commitments, check if the month falls within the commitment period
                $commitmentStart = date('Y-m', strtotime($startDate));
                $commitmentEnd = $endDate ? date('Y-m', strtotime($endDate)) : $month;

                if ($month >= $commitmentStart && $month <= $commitmentEnd) {
                    $totalCommitted += $amount;
                }
            }
        }

        return $totalCommitted;
    }

    /**
     * Calculate number of members who have commitments for a specific month
     *
     * @param string $month Y-m format
     * @return int
     */
    private function calculateMemberCountForMonth($month)
    {
        try {
            $db = \Config\Database::connect();

            $commitments = $db->table('commitments')->get()->getResultArray();
            $memberIds = [];

            foreach ($commitments as $commitment) {
                // Validate required fields
                if (empty($commitment['frequency']) || empty($commitment['start_date']) || empty($commitment['member_id'])) {
                    continue;
                }

                $frequency = $commitment['frequency'];
                $startDate = $commitment['start_date'];
                $endDate = $commitment['end_date'];
                $memberId = $commitment['member_id'];

                if ($frequency === 'one-time') {
                    // For one-time commitments, check if start_date falls within the month
                    $commitmentMonth = date('Y-m', strtotime($startDate));
                    if ($commitmentMonth === $month) {
                        $memberIds[$memberId] = true;
                    }
                } else if ($frequency === 'monthly') {
                    // For monthly commitments, check if the month falls within the commitment period
                    $commitmentStart = date('Y-m', strtotime($startDate));
                    $commitmentEnd = $endDate ? date('Y-m', strtotime($endDate)) : $month;

                    if ($month >= $commitmentStart && $month <= $commitmentEnd) {
                        $memberIds[$memberId] = true;
                    }
                }
            }

            return count($memberIds);
        } catch (\Exception $e) {
            // Log error and return 0 to prevent breaking the chart
            log_message('error', 'Error calculating member count for month ' . $month . ': ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Calculate total paid amount for a specific month
     * Uses payment periods, not payment dates
     *
     * @param string $month Y-m format
     * @return float
     */
    private function calculateTotalPaidForMonth($month)
    {
        $db = \Config\Database::connect();

        $payments = $db->table('payments')->get()->getResultArray();
        $totalPaid = 0;

        foreach ($payments as $payment) {
            $paymentPeriods = $payment['payment_periods'];
            $paymentAmount = $payment['amount'];

            if (empty($paymentPeriods) || $paymentPeriods === 'NULL') {
                // For legacy payments without payment_periods, use payment date
                $paymentDate = $payment['payment_date'];
                $paymentMonth = date('Y-m', strtotime($paymentDate));

                if ($paymentMonth === $month) {
                    $totalPaid += $paymentAmount;
                }
                continue;
            }

            // Parse payment periods - handle both JSON array and comma-separated formats
            $periods = [];

            // Check if it's JSON format (starts with [ and ends with ])
            if (strpos($paymentPeriods, '[') === 0 && strpos($paymentPeriods, ']') !== false) {
                $periodsArray = json_decode($paymentPeriods, true);
                if (is_array($periodsArray)) {
                    $periods = $periodsArray;
                }
            } else {
                // Handle comma-separated format
                $periods = explode(',', $paymentPeriods);
            }

            if (empty($periods)) {
                continue;
            }

            $validPeriods = 0;
            $totalPeriods = count($periods);

            foreach ($periods as $period) {
                $period = trim($period, ' "\''); // Remove quotes and whitespace
                if (strlen($period) >= 7) { // Y-m format
                    $periodMonth = substr($period, 0, 7); // Extract Y-m part
                    if ($periodMonth === $month) {
                        $validPeriods++;
                    }
                }
            }

            // Calculate proportional amount for periods within the month
            if ($validPeriods > 0 && $totalPeriods > 0) {
                $totalPaid += ($paymentAmount * $validPeriods) / $totalPeriods;
            }
        }

        return $totalPaid;
    }

    /**
     * Calculate collection efficiency metrics using period-based logic
     *
     * @return array
     */
    private function calculateCollectionEfficiencyMetrics()
    {
        $db = \Config\Database::connect();

        // 1. Calculate current month collection rate
        $currentMonth = date('Y-m');
        $currentMonthCommitted = $this->calculateTotalCommittedForMonth($currentMonth);
        $currentMonthPaid = $this->calculateTotalPaidForMonth($currentMonth);
        $currentMonthRate = ($currentMonthCommitted > 0) ?
            ($currentMonthPaid / $currentMonthCommitted * 100) : 0;

        // 2. Calculate year-to-date collection rate
        $currentYear = date('Y');
        $ytdCommitted = 0;
        $ytdPaid = 0;

        for ($month = 1; $month <= date('n'); $month++) {
            $monthStr = sprintf('%s-%02d', $currentYear, $month);
            $ytdCommitted += $this->calculateTotalCommittedForMonth($monthStr);
            $ytdPaid += $this->calculateTotalPaidForMonth($monthStr);
        }

        $ytdRate = ($ytdCommitted > 0) ? ($ytdPaid / $ytdCommitted * 100) : 0;

        // 3. Calculate average months to payment using payment periods vs commitment periods
        $avgMonths = $this->calculateAverageMonthsToPayment();

        // 4. Get members with outstanding balances (using existing logic as it's already correct)
        $totalActiveMembers = $this->memberModel->where('status', 'active')->countAllResults();
        $membersWithBalances = $this->summaryModel->where('balance >', 0)->countAllResults();
        $percentWithBalance = ($totalActiveMembers > 0) ?
            ($membersWithBalances / $totalActiveMembers * 100) : 0;

        return [
            'currentMonthRate' => round($currentMonthRate, 1),
            'ytdRate' => round($ytdRate, 1),
            'avgMonths' => $avgMonths,
            'percentWithBalance' => round($percentWithBalance, 1)
        ];
    }

    /**
     * Calculate average months to payment using commitment start date vs actual payment date
     *
     * @return float
     */
    private function calculateAverageMonthsToPayment()
    {
        $db = \Config\Database::connect();

        // Get payments with their commitment details
        $paymentsQuery = $db->table('payments p')
            ->select('p.payment_date, c.start_date')
            ->join('commitments c', 'p.commitment_id = c.commitment_id')
            ->where('p.commitment_id IS NOT NULL')
            ->where('c.start_date IS NOT NULL')
            ->where('p.payment_date IS NOT NULL')
            ->get();

        $payments = $paymentsQuery->getResultArray();
        $totalMonthsDiff = 0;
        $count = 0;

        foreach ($payments as $payment) {
            $paymentDate = $payment['payment_date'];
            $commitmentStartDate = $payment['start_date'];

            if (empty($paymentDate) || empty($commitmentStartDate)) {
                continue;
            }

            // Calculate months difference between commitment start date and payment date
            $startDate = new \DateTime($commitmentStartDate);
            $paidDate = new \DateTime($paymentDate);

            // Only count if payment date is after or equal to start date
            if ($paidDate >= $startDate) {
                $diff = $startDate->diff($paidDate);
                $monthsDiff = ($diff->y * 12) + $diff->m;

                // Add partial month if there are remaining days
                if ($diff->d > 0) {
                    $monthsDiff += round($diff->d / 30, 1);
                }

                $totalMonthsDiff += $monthsDiff;
                $count++;
            }
        }

        return ($count > 0) ? round($totalMonthsDiff / $count, 1) : 0;
    }

    // Method moved to the bottom of the class to avoid duplication

    /**
     * Display the collection summary report
     *
     * @return mixed
     */
    public function collectionSummary()
    {
        // Get filter parameters from the URL
        $excludeZero = $this->request->getGet('exclude_zero') ?? 'commitments';
        $fromDate = $this->request->getGet('from_date');
        $toDate = $this->request->getGet('to_date');

        // Determine filter type
        $excludeZeroBalances = ($excludeZero === 'yes');
        $excludeZeroCommitments = ($excludeZero === 'commitments');

        // Set default date range if not provided (current year)
        if (empty($fromDate)) {
            $fromDate = date('Y-01-01'); // First day of current year
        }
        if (empty($toDate)) {
            $toDate = date('Y-m-t'); // Last day of current month
        }

        // Get summaries with member details and date filtering
        if ($fromDate && $toDate) {
            $summaries = $this->summaryModel->getAllSummariesWithMemberDetailsAndDateFilter($excludeZeroBalances, $excludeZeroCommitments, $fromDate, $toDate);
        } else {
            $summaries = $this->summaryModel->getAllSummariesWithMemberDetails($excludeZeroBalances, $excludeZeroCommitments);
        }

        // Add phone and WhatsApp numbers to the summaries
        $db = \Config\Database::connect();
        foreach ($summaries as &$summary) {
            $memberData = $db->table('members')
                ->select('phone, whatsapp_number')
                ->where('member_id', $summary['member_id'])
                ->get()
                ->getRowArray();

            $summary['phone'] = $memberData['phone'] ?? '';
            $summary['whatsapp_number'] = $memberData['whatsapp_number'] ?? '';
        }

        $data = [
            'title' => 'Collection Summary',
            'summaries' => $summaries,
            'exclude_zero' => $excludeZero,
            'from_date' => $fromDate,
            'to_date' => $toDate
        ];

        return view('reports/collection_summary', $data);
    }

    /**
     * Display the outstanding balances report
     *
     * @return mixed
     */
    public function outstandingBalances()
    {
        $data = [
            'title' => 'Outstanding Balances',
            'members' => $this->summaryModel->getMembersWithOutstandingBalances()
        ];

        return view('reports/outstanding_balances', $data);
    }

    /**
     * Display the payment history report
     *
     * @return mixed
     */
    public function paymentHistory()
    {
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-1 month'));
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');
        $memberId = $this->request->getGet('member_id') ?? null;

        $db = \Config\Database::connect();
        $builder = $db->table('payments p')
            ->select('p.*, m.name as member_name')
            ->join('members m', 'm.member_id = p.member_id')
            ->where('p.payment_date >=', $startDate)
            ->where('p.payment_date <=', $endDate)
            ->orderBy('p.payment_date', 'DESC');

        if ($memberId) {
            $builder->where('p.member_id', $memberId);
        }

        $payments = $builder->get()->getResultArray();

        $data = [
            'title' => 'Payment History',
            'payments' => $payments,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'memberId' => $memberId,
            'members' => $this->memberModel->findAll(),
            'totalAmount' => array_sum(array_column($payments, 'amount'))
        ];

        return view('reports/payment_history', $data);
    }

    /**
     * Display the commitment report
     *
     * @return mixed
     */
    public function commitmentReport()
    {
        $frequency = $this->request->getGet('frequency') ?? null;
        $status = $this->request->getGet('status') ?? 'active';

        $db = \Config\Database::connect();
        $builder = $db->table('commitments c')
            ->select('c.*, m.name as member_name, m.status as member_status')
            ->join('members m', 'm.member_id = c.member_id');

        if ($frequency) {
            $builder->where('c.frequency', $frequency);
        }

        if ($status === 'active') {
            $today = date('Y-m-d');
            $builder->where('c.start_date <=', $today)
                ->groupStart()
                    ->where('c.end_date IS NULL')
                    ->orWhere('c.end_date >=', $today)
                ->groupEnd()
                ->where('m.status', 'active');
        } elseif ($status === 'inactive') {
            $today = date('Y-m-d');
            $builder->where('c.end_date <', $today)
                ->orWhere('m.status', 'inactive');
        }

        $commitments = $builder->get()->getResultArray();

        // Calculate total amount considering frequency and duration
        $totalAmount = 0;
        foreach ($commitments as &$commitment) {
            $amount = $commitment['amount'];
            $frequency = $commitment['frequency'];
            $startDate = new \DateTime($commitment['start_date']);
            $endDate = !empty($commitment['end_date']) ? new \DateTime($commitment['end_date']) : null;

            // For one-time commitments, just add the amount
            if ($frequency == 'one-time') {
                $commitment['calculated_amount'] = $amount;
                $totalAmount += $amount;
                continue;
            }

            // For ongoing commitments (no end date), calculate for 1 year from start date or today
            if ($endDate === null) {
                $endDate = new \DateTime($startDate->format('Y-m-d'));
                $endDate->modify('+1 year');
            }

            // Calculate the number of periods based on frequency
            $interval = $startDate->diff($endDate);

            // Calculate total months more accurately
            $totalMonths = ($interval->y * 12) + $interval->m;
            if ($interval->d > 0) {
                // Add one more month if there are remaining days
                $totalMonths += 1;
            }

            switch ($frequency) {
                case 'monthly':
                    $periods = max(1, $totalMonths); // Ensure at least 1 period
                    break;
                case 'quarterly':
                    $periods = max(1, ceil($totalMonths / 3)); // Ensure at least 1 period
                    break;
                case 'yearly':
                    $periods = max(1, ceil($totalMonths / 12)); // Ensure at least 1 period
                    break;
                default:
                    $periods = 1;
            }

            $calculatedAmount = $amount * $periods;
            $commitment['calculated_amount'] = $calculatedAmount;
            $commitment['periods'] = $periods;
            $totalAmount += $calculatedAmount;
        }

        $data = [
            'title' => 'Commitment Report',
            'commitments' => $commitments,
            'frequency' => $frequency,
            'status' => $status,
            'totalAmount' => $totalAmount
        ];

        return view('reports/commitment_report', $data);
    }

    /**
     * Display the statistics page with graphs
     *
     * @return mixed
     */
    public function statistics()
    {
        // Get date range from request or use default (last 12 months)
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-12 months', strtotime($endDate)));

        // Convert to DateTime objects for easier manipulation
        $startDateTime = new \DateTime($startDate);
        $endDateTime = new \DateTime($endDate);

        // Ensure start date is the first day of the month
        $startDateTime->modify('first day of this month');
        $startDate = $startDateTime->format('Y-m-d');

        // Ensure end date is the last day of the month
        $endDateTime->modify('last day of this month');
        $endDate = $endDateTime->format('Y-m-d');

        // Calculate the number of months in the range
        $interval = $startDateTime->diff($endDateTime);
        $monthCount = ($interval->y * 12) + $interval->m + 1; // +1 to include both start and end months

        // Initialize arrays to store data for each month
        $months = [];
        $committedAmounts = [];
        $paidAmounts = [];

        // Get database connection
        $db = \Config\Database::connect();

        // Loop through each month in the range
        $currentDate = clone $startDateTime;
        for ($i = 0; $i < $monthCount; $i++) {
            // Format month for display (e.g., "Jan 2023")
            $monthLabel = $currentDate->format('M Y');
            $months[] = $monthLabel;

            // Get first and last day of current month
            $firstDay = $currentDate->format('Y-m-01');
            $lastDay = $currentDate->format('Y-m-t');

            // Calculate total committed amount for this month
            $committedQuery = $db->table('commitments')
                ->select('SUM(amount) as total')
                ->where('frequency', 'monthly')
                ->where('start_date <=', $lastDay)
                ->groupStart()
                    ->where('end_date IS NULL')
                    ->orWhere('end_date >=', $firstDay)
                ->groupEnd()
                ->get();

            $committedResult = $committedQuery->getRowArray();
            $committedAmounts[] = (float)($committedResult['total'] ?? 0);

            // Calculate total paid amount for this month
            $paidQuery = $db->table('payments')
                ->select('SUM(amount) as total')
                ->where('payment_date >=', $firstDay)
                ->where('payment_date <=', $lastDay)
                ->get();

            $paidResult = $paidQuery->getRowArray();
            $paidAmounts[] = (float)($paidResult['total'] ?? 0);

            // Move to next month
            $currentDate->modify('+1 month');
        }

        $data = [
            'title' => 'Statistics',
            'startDate' => $startDate,
            'endDate' => $endDate,
            'months' => json_encode($months),
            'committedAmounts' => json_encode($committedAmounts),
            'paidAmounts' => json_encode($paidAmounts)
        ];

        return view('reports/statistics', $data);
    }

    /**
     * Recalculate all collection summaries
     *
     * @return mixed
     */
    public function recalculateAllSummaries()
    {
        $members = $this->memberModel->findAll();

        foreach ($members as $member) {
            $this->summaryModel->recalculateSummary($member['member_id']);
        }

        return redirect()->to('/reports/collection-summary')
            ->with('message', 'All collection summaries have been recalculated');
    }

    /**
     * Display members without active commitments
     *
     * @return mixed
     */
    public function membersWithoutCommitments()
    {
        // Get all members without active commitments
        $membersWithoutActiveCommitments = $this->getMembersWithoutActiveCommitments();

        $data = [
            'title' => 'Inactive Commitments',
            'members' => $membersWithoutActiveCommitments
        ];

        return view('reports/members_without_commitments', $data);
    }

    /**
     * Get members without active commitments
     *
     * @return array
     */
    private function getMembersWithoutActiveCommitments()
    {
        $db = \Config\Database::connect();

        // Get current date
        $currentDate = date('Y-m-d');

        // Find active members who don't have any active commitments
        $query = $db->table('members m')
            ->select('m.*')
            ->where('m.status', 'active')
            ->whereNotIn('m.member_id', function($subquery) use ($currentDate) {
                $subquery->select('c.member_id')
                    ->from('commitments c')
                    ->where('c.start_date <=', $currentDate)
                    ->groupStart()
                        ->where('c.end_date IS NULL')
                        ->orWhere('c.end_date >=', $currentDate)
                    ->groupEnd();
            })
            ->get();

        return $query->getResultArray();
    }

    // Helper methods removed to simplify the code
}
