<?php

namespace App\Controllers;

use App\Models\CommitmentModel;
use CodeIgniter\RESTful\ResourceController;

class Api extends ResourceController
{
    protected $format = 'json';

    /**
     * Get commitments for a specific member with calculated details
     *
     * @param int $memberId
     * @return mixed
     */
    public function getMemberCommitments($memberId)
    {
        log_message('debug', "API getMemberCommitments called for Member ID: {$memberId}");

        $commitmentModel = new CommitmentModel();

        // Get all commitments for this member
        $db = \Config\Database::connect();
        $allCommitments = $db->table('commitments')
            ->where('member_id', $memberId)
            ->orderBy('created_at', 'DESC')
            ->get()
            ->getResultArray();

        log_message('debug', "Direct SQL query found " . count($allCommitments) . " commitments for Member ID: {$memberId}");

        // Check for payments for each commitment
        foreach ($allCommitments as $commitment) {
            $commitmentId = $commitment['commitment_id'];
            $payments = $db->table('payments')
                ->where('commitment_id', $commitmentId)
                ->get()
                ->getResultArray();

            $totalPaid = 0;
            foreach ($payments as $payment) {
                $totalPaid += $payment['amount'];
            }

            log_message('debug', "Commitment ID: {$commitmentId}, Amount: {$commitment['amount']}, Total Paid: {$totalPaid}, Payments Count: " . count($payments));
        }

        // Get all unpaid commitments, regardless of active status
        $commitments = $commitmentModel->getAllUnpaidCommitments($memberId);

        log_message('debug', "Final commitments count: " . count($commitments));

        // If no unpaid commitments, get all active commitments
        if (empty($commitments)) {
            log_message('debug', "No unpaid commitments found for Member ID: {$memberId}");

            // For debugging, let's get all commitments for this member
            $allCommitments = $commitmentModel->where('member_id', $memberId)
                ->orderBy('created_at', 'DESC')
                ->findAll();
            log_message('debug', "Total commitments in database for Member ID {$memberId}: " . count($allCommitments));

            foreach ($allCommitments as $commitment) {
                log_message('debug', "DB Commitment ID: {$commitment['commitment_id']}, Amount: {$commitment['amount']}, Frequency: {$commitment['frequency']}, Start Date: {$commitment['start_date']}, End Date: " . ($commitment['end_date'] ?? 'NULL'));
            }

            // Do NOT fall back to showing all commitments
            // Return an empty array to indicate no unpaid commitments
            log_message('debug', "Returning empty array - no unpaid commitments");
            $commitments = [];
        } else {
            log_message('debug', "Found " . count($commitments) . " unpaid commitments for Member ID: {$memberId}");
        }

        // Add calculated details to each commitment
        foreach ($commitments as &$commitment) {
            $amount = $commitment['amount'];
            $frequency = $commitment['frequency'];
            $startDate = new \DateTime($commitment['start_date']);
            $endDate = !empty($commitment['end_date']) ? new \DateTime($commitment['end_date']) : null;

            // For one-time commitments, just add the amount
            if ($frequency == 'one-time') {
                $commitment['calculated_amount'] = $amount;
                $commitment['periods'] = 1;
                $commitment['period_type'] = 'one-time';
                $commitment['months'] = [];
                continue;
            }

            // For ongoing commitments (no end date), calculate for 1 year from start date or today
            if ($endDate === null) {
                $endDate = new \DateTime($startDate->format('Y-m-d'));
                $endDate->modify('+1 year');
            }

            // Calculate the number of periods based on frequency
            $interval = $startDate->diff($endDate);
            $totalMonths = ($interval->y * 12) + $interval->m + ($interval->d > 0 ? 1 : 0);

            switch ($frequency) {
                case 'monthly':
                    $periods = $totalMonths;
                    $periodType = 'month';
                    break;
                case 'quarterly':
                    $periods = ceil($totalMonths / 3);
                    $periodType = 'quarter';
                    break;
                case 'yearly':
                    $periods = ceil($totalMonths / 12);
                    $periodType = 'year';
                    break;
                default:
                    $periods = 1;
                    $periodType = 'period';
            }

            $commitment['calculated_amount'] = $amount * $periods;
            $commitment['periods'] = $periods;
            $commitment['period_type'] = $periodType;

            // Generate list of months for monthly commitments
            $commitment['months'] = [];
            if ($frequency == 'monthly') {
                $currentDate = clone $startDate;

                // Get all payments for this commitment
                $db = \Config\Database::connect();
                $payments = $db->table('payments')
                    ->where('commitment_id', $commitment['commitment_id'])
                    ->get()
                    ->getResultArray();

                // Extract paid months from payment notes
                $paidMonths = [];
                foreach ($payments as $payment) {
                    if (!empty($payment['notes'])) {
                        // Log the payment notes for debugging
                        log_message('debug', "Payment ID: {$payment['payment_id']}, Notes: " . $payment['notes']);

                        // Extract all month names from the notes
                        $monthPattern = '(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}';

                        // Look for month names in the notes
                        if (preg_match_all('/' . $monthPattern . '/', $payment['notes'], $matches)) {
                            log_message('debug', "Found month names in notes: " . print_r($matches[0], true));

                            foreach ($matches[0] as $monthName) {
                                $monthName = trim($monthName);
                                if (!empty($monthName)) {
                                    // Convert "Month Year" to "Y-m" format
                                    $date = \DateTime::createFromFormat('F Y', $monthName);
                                    if ($date) {
                                        $paidMonthValue = $date->format('Y-m');
                                        $paidMonths[] = $paidMonthValue;
                                        log_message('debug', "Added paid month: {$monthName} -> {$paidMonthValue}");
                                    } else {
                                        log_message('debug', "Failed to parse date: {$monthName}");
                                    }
                                }
                            }
                        } else {
                            log_message('debug', "No month names found in notes using regex");
                        }
                    }
                }

                log_message('debug', "Total paid months found: " . count($paidMonths) . ", Values: " . implode(', ', $paidMonths));

                // Add all months, marking which ones are paid
                log_message('debug', "Adding months for commitment ID: {$commitment['commitment_id']}, Start: {$startDate->format('Y-m-d')}, End: {$endDate->format('Y-m-d')}");

                while ($currentDate <= $endDate) {
                    $monthValue = $currentDate->format('Y-m');
                    $monthLabel = $currentDate->format('F Y');
                    $isPaid = in_array($monthValue, $paidMonths);

                    log_message('debug', "Month: {$monthLabel}, Value: {$monthValue}, Is Paid: " . ($isPaid ? 'Yes' : 'No'));

                    // Only add unpaid months
                    if (!$isPaid) {
                        $commitment['months'][] = [
                            'value' => $monthValue,
                            'label' => $monthLabel
                        ];
                        log_message('debug', "Added unpaid month: {$monthLabel}");
                    } else {
                        log_message('debug', "Skipped paid month: {$monthLabel}");
                    }

                    $currentDate->modify('+1 month');
                }

                log_message('debug', "Total unpaid months added: " . count($commitment['months']));
            }
        }

        return $this->respond($commitments);
    }

    /**
     * Get payment details for a specific commitment
     *
     * @param int $commitmentId
     * @param int $numMonths
     * @return mixed
     */
    public function getPaymentDetails($commitmentId, $numMonths = 1)
    {
        $commitmentModel = new CommitmentModel();
        $commitment = $commitmentModel->find($commitmentId);

        if (!$commitment) {
            return $this->failNotFound('Commitment not found');
        }

        $amount = $commitment['amount'];
        $frequency = $commitment['frequency'];

        // For one-time commitments, just return the amount
        if ($frequency == 'one-time') {
            return $this->respond([
                'amount' => $amount,
                'description' => 'One-time payment'
            ]);
        }

        // For other frequencies, calculate based on number of months/periods
        switch ($frequency) {
            case 'monthly':
                $calculatedAmount = $amount * $numMonths;
                $description = $numMonths . ' month' . ($numMonths > 1 ? 's' : '');
                break;
            // Keep support for legacy quarterly and yearly commitments
            case 'quarterly':
                $calculatedAmount = $amount * ceil($numMonths / 3);
                $description = ceil($numMonths / 3) . ' quarter' . (ceil($numMonths / 3) > 1 ? 's' : '') . ' (Legacy)';
                break;
            case 'yearly':
                $calculatedAmount = $amount * ceil($numMonths / 12);
                $description = ceil($numMonths / 12) . ' year' . (ceil($numMonths / 12) > 1 ? 's' : '') . ' (Legacy)';
                break;
            default:
                $calculatedAmount = $amount;
                $description = '1 period';
        }

        return $this->respond([
            'amount' => $calculatedAmount,
            'description' => $description
        ]);
    }
}
