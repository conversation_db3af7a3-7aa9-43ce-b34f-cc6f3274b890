<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddPaymentPeriodsToPayments extends Migration
{
    public function up()
    {
        $this->forge->addColumn('payments', [
            'payment_periods' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'notes'
            ]
        ]);
    }

    public function down()
    {
        $this->forge->dropColumn('payments', 'payment_periods');
    }
}
