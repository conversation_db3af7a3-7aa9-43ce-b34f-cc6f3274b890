<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Commitments</h1>
    <a href="<?= site_url('commitments/new') ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add New Commitment
    </a>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Member</th>
                        <th>Amount</th>
                        <th>Frequency</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($commitments as $commitment): ?>
                        <tr>
                            <td><?= $commitment['commitment_id'] ?></td>
                            <td>
                                <a href="<?= site_url('members/show/' . $commitment['member_id']) ?>">
                                    <?= $commitment['member_name'] ?>
                                </a>
                            </td>
                            <td><?= format_currency_with_decimals($commitment['amount']) ?></td>
                            <td><?= ucfirst($commitment['frequency']) ?></td>
                            <td><?= date('d M Y', strtotime($commitment['start_date'])) ?></td>
                            <td>
                                <?= $commitment['end_date'] ? date('d M Y', strtotime($commitment['end_date'])) : 'Ongoing' ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?= site_url('commitments/show/' . $commitment['commitment_id']) ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?= site_url('commitments/edit/' . $commitment['commitment_id']) ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $commitment['commitment_id'] ?>" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal<?= $commitment['commitment_id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $commitment['commitment_id'] ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel<?= $commitment['commitment_id'] ?>">Confirm Delete</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                Are you sure you want to delete this commitment? This action cannot be undone.
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <a href="<?= site_url('commitments/delete/' . $commitment['commitment_id']) ?>" class="btn btn-danger">Delete</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
