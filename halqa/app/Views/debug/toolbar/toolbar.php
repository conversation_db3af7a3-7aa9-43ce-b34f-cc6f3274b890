<?php
/**
 * Custom Debug Toolbar template that hides the file numbers
 * while keeping the toolbar functionality.
 */

// Get the toolbar config
$config = config('Toolbar');

// Only show file numbers if explicitly enabled
$displayNumbers = $config->displayNumbers ?? false;

// If numbers should be hidden, we'll use CSS to hide them
if (!$displayNumbers) {
    echo '<style>
        body > *:first-child:not(.navbar):not(#debug-icon):not(.container-fluid):not(.content) {
            display: none !important;
        }
        body::before, body::after {
            display: none !important;
        }
    </style>';
}

// Include the original toolbar template
include SYSTEMPATH . 'Debug/Toolbar/Views/toolbar.php';
