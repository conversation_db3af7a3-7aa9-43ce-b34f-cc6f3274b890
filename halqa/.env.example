#--------------------------------------------------------------------
# Example Environment file
#
# This file can be used as a starting point for your own .env file.
# Copy this file to .env and update the values as needed.
#--------------------------------------------------------------------

# ENVIRONMENT
CI_ENVIRONMENT = development

# APP
app.baseURL = 'http://localhost:8082/'
app.forceGlobalSecureRequests = false
app.sessionDriver = 'CodeIgniter\Session\Handlers\FileHandler'
app.sessionCookieName = 'ci_session'
app.sessionExpiration = 7200
app.sessionSavePath = null
app.sessionMatchIP = false
app.sessionTimeToUpdate = 300
app.sessionRegenerateDestroy = false
app.CSPEnabled = false

# DATABASE - LOCAL DEVELOPMENT
database.default.hostname = localhost
database.default.database = org_accounting
database.default.username = root
database.default.password =
database.default.DBDriver = MySQLi
database.default.DBPrefix =
database.default.port = 3306
database.default.charset = utf8mb4
database.default.DBCollat = utf8mb4_general_ci

# DATABASE - HOSTINGER PRODUCTION (uncomment and update for production)
# database.default.hostname = localhost
# database.default.database = u888771413_halqa
# database.default.username = u888771413_halqa_user
# database.default.password = your_hostinger_password
# database.default.DBDriver = MySQLi
# database.default.DBPrefix =
# database.default.port = 3306
# database.default.charset = utf8mb4
# database.default.DBCollat = utf8mb4_general_ci

# SESSION
session.driver = 'App\Libraries\CustomFileHandler'
session.cookieName = 'ci_session'
session.expiration = 7200
session.savePath = '/absolute/path/to/your/project/writable/session'
session.matchIP = false
session.timeToUpdate = 300
session.regenerateDestroy = false

# SECURITY
security.csrfProtection = 'cookie'
security.tokenRandomize = false
security.tokenName = 'csrf_token_name'
security.headerName = 'X-CSRF-TOKEN'
security.cookieName = 'csrf_cookie_name'
security.expires = 7200
security.regenerate = true
security.redirect = true
security.samesite = 'Lax'

# COOKIE
cookie.prefix = ''
cookie.expires = 0
cookie.path = '/'
cookie.domain = ''
cookie.secure = false
cookie.httponly = false
cookie.samesite = 'Lax'
cookie.raw = false

# ENCRYPTION
encryption.key =
encryption.driver = OpenSSL
encryption.blockSize = 16
encryption.digest = SHA512

# HONEYPOT
honeypot.hidden = 'true'
honeypot.label = 'Fill This Field'
honeypot.name = 'honeypot'
honeypot.template = '<label>{label}</label><input type="text" name="{name}" value=""/>'
honeypot.container = '<div style="display:none">{template}</div>'

# CONTENT SECURITY POLICY
contentsecuritypolicy.reportOnly = false
contentsecuritypolicy.defaultSrc = 'none'
contentsecuritypolicy.scriptSrc = 'self'
contentsecuritypolicy.styleSrc = 'self'
contentsecuritypolicy.imageSrc = 'self'
contentsecuritypolicy.baseURI = null
contentsecuritypolicy.childSrc = null
contentsecuritypolicy.connectSrc = 'self'
contentsecuritypolicy.fontSrc = null
contentsecuritypolicy.formAction = null
contentsecuritypolicy.frameAncestors = null
contentsecuritypolicy.frameSrc = null
contentsecuritypolicy.mediaSrc = null
contentsecuritypolicy.objectSrc = null
contentsecuritypolicy.pluginTypes = null
contentsecuritypolicy.reportURI = null
contentsecuritypolicy.sandbox = false
contentsecuritypolicy.upgradeInsecureRequests = false
contentsecuritypolicy.styleNonceTag = '{csp-style-nonce}'
contentsecuritypolicy.scriptNonceTag = '{csp-script-nonce}'
contentsecuritypolicy.autoNonce = true

# CORS
cors.allowedOrigins = ['*']
cors.allowedOriginsPatterns = []
cors.allowedMethods = ['GET', 'POST', 'OPTIONS', 'PUT', 'DELETE']
cors.allowedHeaders = ['*']
cors.exposedHeaders = []
cors.maxAge = 7200
cors.supportsCredentials = false

# CACHE
cache.handler = 'file'
cache.backupHandler = 'dummy'
cache.ttl = 60
cache.prefix = 'ci_'

# LOGGER
logger.threshold = 4
